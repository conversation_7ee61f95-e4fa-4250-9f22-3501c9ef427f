import { useTranslation } from 'react-i18next';

import {
  AxonButton,
  AxonCard,
  AxonDropdownMenu,
  AxonDropdownMenuContent,
  AxonDropdownMenuItem,
  AxonDropdownMenuTrigger,
  AxonSeparator,
} from 'ui/UIComponents';

import useTabStore from '@/stores/tab.store';
import { useGetCustomerInfo } from 'services/Customer';
import { BoldCard, BoldHome, Book, MoreHorizontal } from 'ui/UIAssets';
import CustomerBadges from './CustomerBadges';
import CustomerDetails from './CustomerDetails';
import { CustomerInfoSkeleton } from './CustomerInfoSkeleton';
import CustomerViewNumber from './CustomerViewNumber';
import ISPInfo from './ISPInfo';
import LogDrawer from '@/components/Logs/LogDrawer';
import { useState } from 'react';

const CustomerInfo = () => {
  const { t } = useTranslation();
  const activeTab = useTabStore((state) => state.activeTab);
  const { data: customerInfo, isLoading } = useGetCustomerInfo(activeTab?.id ?? '');
  const [openDrawer, setOpenDrawer] = useState(false);

  if (!customerInfo?.data || isLoading) {
    return <CustomerInfoSkeleton />;
  }
  const customer = customerInfo?.data?.customer;
  return (
    <AxonCard>
      <div className='flex h-[135px] flex-row items-center gap-x-5 p-5 font-medium'>
        <div className='border-gradient-border rounded-md border p-2'>
          <BoldHome className='text-content-tertiary size-16' />
        </div>

        <div className='scrollbar-md flex flex-col gap-y-1 overflow-auto'>
          <p className='text-content-tertiary text-xs uppercase leading-tight'>
            {t('customer:customerInfo.customerNetwork')}
          </p>
          <div className='mb-2 flex items-center gap-2'>
            <p className='text-2xl leading-tight'>{customer?.networkId}</p>
            <CustomerBadges />
          </div>
          <div className='flex items-center gap-3'>
            <CustomerDetails />
            <ISPInfo />
            {(customer?.servicePlanName || customer?.phoneNumber) && (
              <AxonSeparator orientation='vertical' className='h-3' />
            )}
            <div className='flex items-center gap-1 *:text-sm'>
              {customer?.servicePlanName && (
                <p className='text-component-hyperlink flex items-center gap-1'>
                  <BoldCard className='size-4' aria-hidden={true} />
                  {customer?.servicePlanName}
                </p>
              )}
              {customer?.phoneNumber && <CustomerViewNumber phoneNumber={customer?.phoneNumber} />}
            </div>
          </div>
        </div>
        <div className='ml-auto'>
          <AxonDropdownMenu>
            <AxonDropdownMenuTrigger>
              <AxonButton variant={'outline'} size='icon'>
                <MoreHorizontal />
              </AxonButton>
            </AxonDropdownMenuTrigger>
            <AxonDropdownMenuContent>
              <AxonDropdownMenuItem className='cursor-pointer' onClick={() => setOpenDrawer(true)}>
                <Book className='mr-2 size-4' />
                Log List
              </AxonDropdownMenuItem>
            </AxonDropdownMenuContent>
          </AxonDropdownMenu>
        </div>
      </div>
      <LogDrawer open={openDrawer} onOpenChange={setOpenDrawer} />
    </AxonCard>
  );
};

export default CustomerInfo;
