import { Phone } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AxonAlertDialogWrapper, AxonButton } from 'ui/UIComponents';

type Props = {
  phoneNumber: number;
};

const CustomerViewNumber = ({ phoneNumber }: Props) => {
  const { t } = useTranslation();
  const [isNumberVisible, setIsNumberVisible] = useState(false);

  const handleViewWithConsent = () => {
    setIsNumberVisible(true);
  };

  return (
    <div>
      {isNumberVisible ? (
        <p className='text-component-hyperlink ml-2 flex items-center gap-1 font-medium'>
          <Phone className='size-4' />
          {phoneNumber}
        </p>
      ) : (
        <AxonAlertDialogWrapper
          trigger={
            <AxonButton
              className='text-component-hyperlink h-auto py-1 font-medium'
              variant='ghost'
              startDecorator={<Phone className='size-4' />}>
              {t('View number')}
            </AxonButton>
          }
          title={t('Warning')}
          description={t('This information is sensitive and requires verbal or written consent from the customer.')}
          confirmText={t('View with consent')}
          cancelText={t('Cancel')}
          onConfirm={handleViewWithConsent}
        />
      )}
    </div>
  );
};

export default CustomerViewNumber;
