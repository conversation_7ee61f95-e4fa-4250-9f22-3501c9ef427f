import useTabStore from '@/stores/tab.store';
import { useTranslation } from 'react-i18next';
import { useGetCustomerInfo } from 'services/Customer';
import { BoldCardSend, BoldCrownMinimalistic, BoldRoundDoubleAltArrowUp } from 'ui/UIAssets';
import { AxonTooltip, AxonTooltipContent, AxonTooltipProvider, AxonTooltipTrigger } from 'ui/UIComponents';

const CustomerBadges = () => {
  const { t } = useTranslation();

  const activeTab = useTabStore((state) => state.activeTab);
  const { data: customerInfo } = useGetCustomerInfo(activeTab?.id ?? '');

  const badges = customerInfo?.data?.customer?.badges;
  return (
    <div className='text-content-secondary flex gap-2 text-xs'>
      {badges?.isVip && (
        <div className='border-gradient-border flex items-center gap-1 rounded-md border px-2 py-1'>
          <BoldCrownMinimalistic className='size-4' />
          VIP
        </div>
      )}
      {badges?.isHighARPU && (
        <div className='border-gradient-border flex items-center gap-1 rounded-md border px-2 py-1'>
          <BoldCardSend className='size-4' />
          {t('highARPU')}
        </div>
      )}
      {badges?.heavyUserPercentage && badges?.heavyUserPercentage > 0 && (
        <div className='border-gradient-border flex items-center gap-1 rounded-md border px-2 py-1'>
          <AxonTooltipProvider delayDuration={100}>
            <AxonTooltip>
              <AxonTooltipTrigger className='flex items-center gap-1'>
                <BoldRoundDoubleAltArrowUp className='size-4' />
                {t('heavyUser')}
              </AxonTooltipTrigger>
              <AxonTooltipContent>{t('heavyUserTooltip', { count: badges?.heavyUserPercentage })}</AxonTooltipContent>
            </AxonTooltip>
          </AxonTooltipProvider>
        </div>
      )}
    </div>
  );
};

export default CustomerBadges;
