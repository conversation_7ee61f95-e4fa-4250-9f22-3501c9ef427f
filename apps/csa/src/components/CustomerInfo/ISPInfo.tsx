import useTabStore from '@/stores/tab.store';
import { t } from 'i18next';
import { useGetCustomerInfo } from 'services/Customer';
import { BoldCity, BoldClockCircle, BoldHomeWifi, BoldLinkCircle, BoldSmartPhone, BoldStation } from 'ui/UIAssets';
import {
  AxonTooltip,
  AxonTooltipContent,
  AxonTooltipPortal,
  AxonTooltipProvider,
  AxonTooltipTrigger,
} from 'ui/UIComponents';

const ISPInfo = () => {
  const activeTab = useTabStore((state) => state.activeTab);
  const { data: customerInfo } = useGetCustomerInfo(activeTab?.id ?? '');

  const isp = customerInfo?.data?.isp;

  return (
    <AxonTooltipProvider delayDuration={100}>
      <AxonTooltip>
        <AxonTooltipTrigger>
          <p className='text-content-secondary flex items-center gap-2 text-sm font-medium'>
            <BoldStation aria-hidden={true} />
            {`${isp?.name} ${isp?.timezoneUTC ? `(UTC${isp?.timezoneUTC})` : ''}`}
          </p>
        </AxonTooltipTrigger>
        <AxonTooltipPortal>
          <AxonTooltipContent className='[&_p:first-child]:font-book [&_p:first-child]:text-content-secondary scrollbar-lg flex max-h-80 flex-col gap-y-5 overflow-y-auto'>
            <div className='flex flex-col gap-1'>
              <p>{t('customer:customerInfo.ispInfo.isp')}</p>
              <p className='flex items-center gap-1'>
                <BoldStation aria-hidden={true} />
                {isp?.name}
              </p>
            </div>
            <div className='flex flex-col gap-1'>
              <p>{t('customer:customerInfo.ispInfo.city/province')}</p>
              <p className='flex items-center gap-1'>
                <BoldCity aria-hidden={true} className='size-4' />
                {isp?.city}
              </p>
            </div>
            <div className='flex flex-col gap-1'>
              <p>{t('customer:customerInfo.ispInfo.timezone')}</p>
              <p className='flex items-center gap-1'>
                <BoldClockCircle aria-hidden={true} className='size-4' />
                {`${isp?.name} ${isp?.timezoneUTC ? `(UTC${isp?.timezoneUTC})` : ''}`}
              </p>
            </div>
            <div className='flex flex-col gap-1'>
              <p>{t('customer:customerInfo.ispInfo.wanType')}</p>
              <p className='flex items-center gap-1'>
                <BoldLinkCircle aria-hidden={true} />
                {isp?.wanType}
              </p>
            </div>
            <div className='flex flex-col gap-1'>
              <p>{t('customer:customerInfo.ispInfo.connectedExtenders')}</p>
              <p className='flex items-center gap-1'>
                <BoldHomeWifi aria-hidden={true} className='size-4' />
                {isp?.connectedExtenders?.supported} {t('customer:customerInfo.ispInfo.official')},{' '}
                {isp?.connectedExtenders?.unsupported} {t('customer:customerInfo.ispInfo.thirdParty')}
              </p>
            </div>
            <div className='flex flex-col gap-1'>
              <p>{t('customer:customerInfo.ispInfo.connectedClients')}</p>
              <p className='flex items-center gap-1'>
                <BoldSmartPhone aria-hidden={true} className='size-4' />
                {isp?.connectedClients?.ethernet} {t('customer:customerInfo.ispInfo.ethernet')},{' '}
                {isp?.connectedClients?.wifi} {t('customer:customerInfo.ispInfo.wifi')}
              </p>
            </div>
          </AxonTooltipContent>
        </AxonTooltipPortal>
      </AxonTooltip>
    </AxonTooltipProvider>
  );
};

export default ISPInfo;
