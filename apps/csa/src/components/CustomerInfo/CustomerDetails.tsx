import useTabStore from '@/stores/tab.store';
import { useGetCustomerInfo } from 'services/Customer';
import { BoldUserCircle } from 'ui/UIAssets';

const CustomerDetails = () => {
  const activeTab = useTabStore((state) => state.activeTab);
  const { data: customerInfo } = useGetCustomerInfo(activeTab?.id ?? '');

  const customer = customerInfo?.data?.customer;
  if (!customer?.name && !customer?.id) return null;

  return (
    <p className='text-content-secondary flex items-center gap-1 text-sm font-medium'>
      <BoldUserCircle aria-hidden={true} className='size-4' />
      {`${customer?.name || 'N/A'} (${customer?.id || 'N/A'})`}
      <sup aria-hidden={true} className='bg-content-meta-red size-1 rounded-full'></sup>
    </p>
    // <AxonTooltipProvider delayDuration={100}>
    //   <AxonTooltip>
    //     <AxonTooltipTrigger>
    //       <p className='text-content-secondary flex items-center gap-1 text-sm font-medium'>
    //         <BoldUserCircle aria-hidden={true} className='size-4' />
    //         {`${customer?.name || 'N/A'} (${customer?.id || 'N/A'})`}
    //         <sup aria-hidden={true} className='bg-content-meta-red size-1 rounded-full'></sup>
    //       </p>
    //     </AxonTooltipTrigger>
    //     <AxonTooltipPortal>
    //       <AxonTooltipContent className='[&_p:first-child]:font-book [&_p:first-child]:text-content-secondary scrollbar-lg flex max-h-80 flex-col gap-y-5 overflow-y-auto'>
    //         <div className='flex flex-col gap-1'>
    //           <p>{t('subscriberName')}</p>
    //           <p className='flex items-center gap-1'>
    //             <BoldUserCircle aria-hidden={true} className='size-4' />
    //             {customer?.name}
    //           </p>
    //         </div>
    //         <div className='flex flex-col gap-1'>
    //           <p>{t('subscriberId')}</p>
    //           <p className='flex items-center gap-1'>
    //             <BoldHashtagSquare aria-hidden={true} className='size-4' />
    //             {customer?.id}
    //           </p>
    //         </div>
    //         <div className='flex flex-col gap-1'>
    //           <p>{t('subscribedSince')}</p>
    //           <p className='flex items-center gap-1'>
    //             <BoldCalendar aria-hidden={true} className='size-4' />
    //             {customer?.subscribedSince}
    //           </p>
    //         </div>
    //         <div className='flex flex-col gap-1'>
    //           <p>{t('networkOwned')}</p>
    //           <p className='flex items-center gap-1'>
    //             <BoldWifiRouterMinimalistic aria-hidden={true} className='size-4' />
    //             {customer?.networkOwned}
    //           </p>
    //         </div>
    //         <div className='flex flex-col gap-1'>
    //           <p>{t('membershipLevel')}</p>
    //           <p className='flex items-center gap-1'>
    //             <BoldCrownMinimalistic aria-hidden={true} className='size-4' />
    //             {customer?.membershipLevel}
    //           </p>
    //         </div>
    //         <div className='flex flex-col gap-1'>
    //           <p>{t('overduePayments')}</p>
    //           <p className='text-content-insights-alert flex items-center gap-1'>
    //             <BoldClockCircle aria-hidden={true} className='size-4' />
    //             <div className='flex flex-col gap-1'>
    //               <span>{t('overduePaymentsDay', { count: customer?.overduePayments?.days })}</span>
    //               <span>{t('dayleftToSuspend', { count: customer?.overduePayments?.daysToSuspend })}</span>
    //             </div>
    //           </p>
    //         </div>
    //         <div className='flex flex-col gap-1'>
    //           <p>{t('passCallHistory')}</p>
    //           <p className='flex items-center gap-1'>
    //             <BoldCalendar aria-hidden={true} className='size-4' />
    //             <div className='flex flex-col gap-1'>
    //               <span>{t('last30Days', { count: customer?.passCallHistory?.last30Days })}</span>
    //               <span>{t('monthBefore', { count: customer?.passCallHistory?.monthBefore })}</span>
    //             </div>
    //           </p>
    //         </div>
    //         <div className='flex flex-col gap-1'>
    //           <p>{t('lastCalledOn')}</p>
    //           <p className='flex items-center gap-1'>
    //             <BoldCalendar aria-hidden={true} className='size-4' />
    //             <div className='flex flex-col gap-1'>
    //               {`${customer?.lastCalledOn} (${t('lastCalledOnDay', { count: customer?.lastCalledDayAgo })})`}
    //             </div>
    //           </p>
    //         </div>
    //       </AxonTooltipContent>
    //     </AxonTooltipPortal>
    //   </AxonTooltip>
    // </AxonTooltipProvider>
  );
};

export default CustomerDetails;
