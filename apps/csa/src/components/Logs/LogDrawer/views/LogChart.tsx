import { useEffect, useMemo, useRef, useState } from 'react';
import { DATETIME_FORMAT, getDateFromUnixTime } from 'services/Utils';
import * as vis from 'vis-timeline/standalone';
import 'vis-timeline/styles/vis-timeline-graph2d.min.css';
import { LogProps } from '..';
import './logChart.css';

function countEventTypes(events: LogProps['logs']): Record<string, number> {
  const counts: Record<string, number> = {};

  for (const event of events) {
    const type = event.eventType;
    counts[type] = (counts[type] || 0) + 1;
  }

  return counts;
}

const LogChart = ({ logs }: LogProps) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const timelineRef = useRef<vis.Timeline | null>(null);
  const [selectedLog, setSelectedLog] = useState<LogProps['logs'][number]>();
  const [popupPosition, setPopupPosition] = useState({ x: 0, y: 0 });

  const eventStats = useMemo(() => countEventTypes(logs), [logs]);

  useEffect(() => {
    if (!containerRef.current) return;

    const formattedLogs = logs.map((l, index) => ({
      id: index,
      content: l.eventType,
      start: getDateFromUnixTime(l.timeStamp)?.format() || '',
    }));

    const items = new vis.DataSet(formattedLogs);

    const options: vis.TimelineOptions = {
      xss: { disabled: false, filterOptions: { whiteList: { p: ['class'] } } },
      height: '100%',
      align: 'left',
      margin: { item: 10, axis: 20 },
      zoomMin: 1000 * 60 * 60,
      zoomMax: 1000 * 60 * 60 * 24 * 7,

      template: function (item) {
        return `
        <p class="log-info log-text">
          ${item.content}
        </p>`;
      },
    };

    timelineRef.current = new vis.Timeline(containerRef.current, items, options);

    timelineRef.current.on('select', (props: { items: number[]; event: { center: { x: number; y: number } } }) => {
      const id = props.items[0];
      const selectedElement = containerRef.current?.querySelector('.vis-item.vis-selected');
      if (!selectedElement || !containerRef.current) return;
      const containerRect = containerRef.current.getBoundingClientRect();
      setSelectedLog(logs[id]);

      setPopupPosition({
        x: props.event.center.x - containerRect.left,
        y: props.event.center.y - containerRect.top,
      });
    });

    timelineRef.current.on('mouseDown', () => {
      setSelectedLog(undefined);
    });

    return () => {
      timelineRef.current?.destroy();
    };
  }, [logs]);

  return (
    <div id='event-time-line-container' className='relative flex size-full flex-col'>
      <div id='event-time-line' ref={containerRef} className='flex-1' />

      <div className='scrollbar-lg grid h-[300px] auto-rows-[40px] grid-cols-3 overflow-auto p-6'>
        {Object.entries(eventStats).map(([type, count]) => (
          <p key={type}>
            {type}: {count}
          </p>
        ))}
      </div>
      {selectedLog && (
        <div
          className='bg-surface-body absolute z-10 flex flex-col gap-y-2 rounded border p-3 shadow-md'
          style={{
            top: popupPosition.y,
            left: popupPosition.x,
          }}>
          <p className='font-semibold'>{selectedLog.eventType}</p>
          <p className=''>{selectedLog.description}</p>
          <p className='text-content-tertiary'>
            {getDateFromUnixTime(selectedLog.timeStamp)?.format(DATETIME_FORMAT.DATE_TIME)}
          </p>
        </div>
      )}
    </div>
  );
};

export default LogChart;
