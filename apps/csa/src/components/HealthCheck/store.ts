import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { HealthCheckStore } from './types';
import useTabStore from '@/stores/tab.store';
import { TOTAL_SLICES } from './utils/wheel.util';

const useHealthCheckStore = create<HealthCheckStore>()(
  immer(
    devtools(
      (set) => ({
        paths: [],
        selectedSlice: null,
        nextSlice: () => {
          set(
            (state) => {
              state.selectedSlice = ((state.selectedSlice ?? 0) + 1) % TOTAL_SLICES;
              useTabStore.getState().addConfig({ healthCheck: { selectedSlice: state.selectedSlice ?? null } });
            },
            undefined,
            { type: 'nextSlice' },
          );
        },
        prevSlice: () => {
          set(
            (state) => {
              state.selectedSlice =
                (state.selectedSlice ?? 0) - 1 < 0 ? TOTAL_SLICES - 1 : (state.selectedSlice ?? 0) - 1;
              useTabStore.getState().addConfig({ healthCheck: { selectedSlice: state.selectedSlice ?? null } });
            },
            undefined,
            { type: 'prevSlice' },
          );
        },
        setPaths: (paths) => {
          set(
            (state) => {
              state.paths = paths;
            },
            undefined,
            { type: 'setPaths', paths },
          );
        },

        setSelectedSlice: (selectedSlice) => {
          set(
            (state) => {
              state.selectedSlice = selectedSlice;
              useTabStore.getState().addConfig({ healthCheck: { selectedSlice: state.selectedSlice ?? null } });
            },
            undefined,
            { type: 'setSelectedSlice', selectedSlice },
          );
        },
      }),
      { name: 'HealthCheckStore', store: 'HealthCheckStore' },
    ),
  ),
);

export default useHealthCheckStore;
