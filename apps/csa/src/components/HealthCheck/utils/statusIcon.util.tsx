import { Polygon2, Signifier, Stroke } from 'ui/UIAssets';
import { EHealthCheckStatus } from '../enum';

export const getStatusIcon = (status?: EHealthCheckStatus | null) => {
  switch (status) {
    case EHealthCheckStatus.STABLE:
      return (
        <div className='bg-content-meta-green/15 flex size-6 items-center justify-center rounded-sm'>
          <Signifier className='text-content-meta-green' />
        </div>
      );
    case EHealthCheckStatus.UNSTABLE:
      return (
        <div className='bg-content-meta-yellow/15 flex size-6 items-center justify-center rounded-sm'>
          <Polygon2 className='text-content-meta-yellow size-4' />
        </div>
      );
    case EHealthCheckStatus.VERY_UNSTABLE:
      return (
        <div className='bg-content-meta-red/15 flex size-6 items-center justify-center rounded-sm'>
          <Stroke className='text-content-meta-red' />
        </div>
      );
    default:
      return (
        <div className='bg-content-disabled/15 flex size-6 items-center justify-center rounded-sm'>
          <Signifier className='text-content-disabled' />
        </div>
      );
  }
};

export const getStatusText = (status?: EHealthCheckStatus | null) => {
  switch (status) {
    case EHealthCheckStatus.STABLE:
      return 'Stable';
    case EHealthCheckStatus.UNSTABLE:
      return 'Unstable';
    case EHealthCheckStatus.VERY_UNSTABLE:
      return 'Very Unstable';
    default:
      return 'N/A';
  }
};
