import { CPESections, CPEWidgets } from '@/constants/cpeSections';
import { BoldBranchingPathUp, BoldClockSquare, BoldHeartPulse, BoldSquareArrowDown } from 'ui/UIAssets';
import { AxonTooltipWrapper } from 'ui/UIComponents';
import { EHealthCheckStatus } from '../../enum';

import { ScrollTo } from '@/types/devices.type';
import { useTranslation } from 'react-i18next';
import { getRelativeTimeFromNow } from 'services/Utils';
import { IBroadbandTest, IInternetUsage, ILinkQuality, ISpeedTest, IWan } from '../../types';
import { getStatusText } from '../../utils/statusIcon.util';
import ItemDetail from '../ItemDetail';

interface IWanMetric {
  icon: React.ReactNode;
  title: string;
  description: string;
  detail: { status: EHealthCheckStatus | null; description: string }[];
  status: EHealthCheckStatus | null;
}

export default function WanDetail({
  data,
  onClickToNavigate,
}: {
  data: IWan;
  onClickToNavigate: (deviceId?: string, scrollTo?: ScrollTo) => void;
}) {
  const { t } = useTranslation();
  if (!data) return null;

  return (
    <>
      {Object.entries(data).map(([metric, value]) => {
        if (metric === 'rootDeviceId') return null;
        const consumeData: IWanMetric = {
          icon: null,
          title: '',
          description: '',
          detail: [],
          status: null,
        };
        // let Icon;
        if (metric === 'speedTest') {
          const metricData = value as ISpeedTest;
          consumeData.icon = <BoldClockSquare className='text-content-tertiary' />;
          consumeData.title = t('customer:healthCheck.wheel.wan.speedTest');
          const downloadSpeed = metricData.downloadSpeed !== null ? metricData.downloadSpeed + ' Mbps' : 'N/A';
          const uploadSpeed = metricData.uploadSpeed !== null ? metricData.uploadSpeed + ' Mbps' : 'N/A';
          const latency = metricData.latency !== null ? metricData.latency + ' ms' : 'N/A';
          consumeData.description = `${downloadSpeed} / ${uploadSpeed} / ${latency}`;
        } else if (metric === 'broadbandTest') {
          const metricData = value as IBroadbandTest;
          consumeData.icon = <BoldBranchingPathUp className='text-content-tertiary' />;
          consumeData.title = t('customer:healthCheck.wheel.wan.broadbandCongestion');
          consumeData.status = metricData.status;
          consumeData.description = `${getStatusText(metricData.status)}`;
        } else if (metric === 'linkQuality') {
          const metricData = value as ILinkQuality;
          const { lastBootTime, isAlive } = metricData;
          consumeData.icon = <BoldHeartPulse className='text-content-tertiary' />;
          consumeData.title = t('customer:healthCheck.wheel.wan.linkQuality');
          consumeData.status = metricData.status;
          consumeData.description = `${getStatusText(metricData.status)}`;
          const latencyDescription =
            (metricData.latency !== null ? `${metricData.latency} ms` : 'N/A') +
            ` ${t('customer:healthCheck.latency')}`;
          const jitterDescription =
            (metricData.jitter !== null ? `${metricData.jitter} ms` : 'N/A') + ` ${t('customer:healthCheck.jitter')}`;
          const errorRateDescription =
            (metricData.errorRate !== null ? `${metricData.errorRate} %` : 'N/A') +
            ` ${t('customer:healthCheck.wheel.wan.errorRate')}`;
          const downCountDescription =
            (metricData.downCount !== null ? `${metricData.downCount}` : 'N/A') +
            ` ${t('customer:healthCheck.wheel.wan.downCount')}`;
          consumeData.detail = [
            {
              // active time
              status: isAlive ? EHealthCheckStatus.STABLE : EHealthCheckStatus.UNKNOWN,
              description: isAlive
                ? `${t('customer:healthCheck.wheel.wan.activeSince')} ${lastBootTime ? getRelativeTimeFromNow(lastBootTime) : 'N/A'}`
                : `Inactive`,
            },
            {
              // latency
              status: metricData.latencyStatus,
              description: latencyDescription,
            },
            {
              // jitter
              status: metricData.latencyStatus,
              description: jitterDescription,
            },
            {
              // error rate
              status: metricData.errorRateStatus,
              description: errorRateDescription,
            },
            {
              // down count
              status: EHealthCheckStatus.STABLE,
              description: downCountDescription,
            },
          ];
        } else if (metric === 'internetUsage') {
          const metricData = value as IInternetUsage;
          consumeData.icon = <BoldSquareArrowDown className='text-content-tertiary' />;
          const downloadUsage = metricData.downloadUsage !== null ? metricData.downloadUsage + ' GB' : 'N/A';
          const uploadUsage = metricData.uploadUsage !== null ? metricData.uploadUsage + ' GB' : 'N/A';
          const isShowUsage = metricData.downloadUsage !== null && metricData.uploadUsage !== null;
          consumeData.title = t('customer:healthCheck.wheel.wan.internetUsage');
          consumeData.description = `${isShowUsage ? `${downloadUsage} / ${uploadUsage}` : 'N/A'}`;
        }
        return (
          <div
            key={metric}
            className='p-xs border-border-flat flex flex-row items-center gap-3 border-b last:border-0'
            onClick={() =>
              onClickToNavigate(data.rootDeviceId, { section: CPESections.WAN, widget: CPEWidgets.WAN_STATISTICS })
            }
            role='button'
            tabIndex={0}>
            <div className='w-d10 h-d10 border-gradient-border bg-surface-action flex items-center justify-center rounded-sm border'>
              {consumeData.icon}
            </div>
            <div className='min-w-0 flex-1'>
              <div className='mb-2xs'>
                <AxonTooltipWrapper
                  label={
                    <p className='text-content-tertiary font-suisses font-book w-full min-w-0 truncate text-xs leading-[120%]'>
                      {consumeData.title}
                    </p>
                  }
                  content={<p>{consumeData.title}</p>}
                />
              </div>
              <div className='flex flex-col'>
                <div className='mb-xs'>
                  <ItemDetail
                    variant='bold'
                    iconName={consumeData.status}
                    description={consumeData.description}
                    color={'rgb(var(--content-primary))'}
                  />
                </div>
                <div className='flex flex-col gap-1'>
                  {consumeData.detail?.map((dt, idx) => (
                    <ItemDetail key={idx} iconName={dt.status} description={dt.description} />
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </>
  );
}
