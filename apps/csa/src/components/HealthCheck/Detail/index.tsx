import { default as CloseIcon } from '@/assets/vector.svg';
import ClientsDrawer from '@/components/drawers/ClientsDrawer';
import { HealthCheckDetailSkeleton } from '@/components/HealthCheck/Detail/Skeleton';
import { CPESections, CPEWidgets } from '@/constants/cpeSections';
import useTabStore from '@/stores/tab.store';
import { ScrollTo } from '@/types/devices.type';
import { getTabUrlSearchParams } from '@/utils/urlTabSyncHandlers';
import { ArrowDown, ArrowUp } from 'lucide-react';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { getRelativeTimeFromNow } from 'services/Utils';
import { BoldLinkRoundAngle, BoldTranslation, Circle, getDeviceImage, Signifier, Triangle, X } from 'ui/UIAssets';
import { AxonButton, AxonCard, AxonSeparator, AxonTooltipWrapper } from 'ui/UIComponents';
import { useShallow } from 'zustand/react/shallow';
import useHealthCheckStore from '../store';
import { BaseOverview, IClient, ICpe, IHealthCheck, INetwork, IService, IWan, Slice } from '../types';
import { getStatusText } from '../utils/statusIcon.util';
import ItemDetail from './ItemDetail';
import WanDetail from './wans';

interface IFooterInfo {
  stable: number;
  unstable: number;
  veryUnstable: number;
  disconnected: number;
  ethernetConnection: number;
  wifiConnection: number;
  unknownConnection: number;
}

interface IProps {
  detail: {
    title: string;
    data: IHealthCheck['details'][keyof IHealthCheck['details']];
    description?: string;
    footerInfo?: IFooterInfo;
  };
  isLoading: boolean;
  overview: BaseOverview & { status: string };
  itemType: Slice;
}

const Detail = (props: IProps) => {
  const { detail, itemType, overview, isLoading } = props;
  const { data, title, description } = detail;
  const { activeTab } = useTabStore(
    useShallow((state) => ({
      activeTab: state.activeTab,
    })),
  );
  const navigate = useNavigate();
  const onClickToNavigate = useCallback(
    (deviceId?: string, scrollTo?: ScrollTo) => {
      if (!deviceId) return;

      navigate(
        `?${getTabUrlSearchParams({
          id: deviceId,
          type: 'device',
          customerId: activeTab?.customerId,
          anchor: scrollTo?.widget ? scrollTo.widget : scrollTo?.section,
        })}`,
      );
    },
    [activeTab?.customerId, navigate],
  );

  const { setSelectedSlice, prevSlice, nextSlice } = useHealthCheckStore(
    useShallow((state) => ({
      selectedSlice: state.selectedSlice,
      setSelectedSlice: state.setSelectedSlice,
      prevSlice: state.prevSlice,
      nextSlice: state.nextSlice,
    })),
  );

  const DataContent = useMemo(() => {
    if (isLoading) {
      return [1, 2, 3, 4, 5].map((_, idx) => <HealthCheckDetailSkeleton key={idx} />);
    }

    if (data && itemType === Slice.WAN) {
      return <WanDetail data={data as IWan} onClickToNavigate={onClickToNavigate} />;
    }

    if (Array.isArray(data) && itemType !== Slice.WAN) {
      return data.map((item, idx) => (
        <Item key={idx} data={item} type={itemType} onClickToNavigate={onClickToNavigate} />
      ));
    }

    return null;
  }, [data, isLoading, itemType, onClickToNavigate]);

  return (
    <div className='health-check-detail flex flex-col gap-y-5'>
      <AxonCard className='relative flex h-[500px] flex-col overflow-hidden'>
        <div className='scrollbar-hidden flex-1 overflow-auto'>
          <div className='header h-d14 bg-surface-popover pl-xl pr-sm border-border-flat sticky top-0 z-10 flex items-center border-b'>
            <p className='font-suisse text-content-primary text-lg font-medium leading-[120%]'>
              {title} <span className='text-content-tertiary font-book leading-[120%]'>{description}</span>
            </p>
            <div className='ml-auto flex'>
              {itemType === Slice.CLIENTS && <ClientsDrawer />}
              <AxonButton
                className='p-2'
                variant='ghost'
                size='icon'
                onClick={() => {
                  setSelectedSlice(null);
                }}>
                <X size={1.5} className='text-content-primary size-4' />
              </AxonButton>
            </div>
          </div>
          <div className='body p-sm'>{DataContent}</div>
        </div>
        <Footer type={itemType} overview={overview} footerInfo={detail.footerInfo} />
      </AxonCard>
      <div className='mt-10 flex flex-row items-center gap-x-3'>
        <AxonButton variant='outline' size='icon' onClick={nextSlice}>
          <ArrowUp size={16} />
        </AxonButton>
        <AxonButton variant='outline' size='icon' onClick={prevSlice}>
          <ArrowDown size={16} />
        </AxonButton>
        <p className='text-muted-foreground text-sm'>to navigate</p>
      </div>
      <div>
        <AxonButton
          variant='outline'
          onClick={() => {
            setSelectedSlice(null);
          }}>
          ESC
        </AxonButton>
      </div>
    </div>
  );
};

interface IItemProps {
  data: IClient | INetwork | IWan | ICpe | IService;
  type: Slice;
  onClickToNavigate: (deviceId?: string, scrollTo?: ScrollTo) => void;
}

function Item(props: IItemProps) {
  const { t } = useTranslation();
  const { data, type, onClickToNavigate } = props;
  switch (type) {
    case Slice.CLIENTS: {
      const client = data as IClient;
      const description1 = client.isOnline ? getStatusText(client.status) : 'Disconnected';
      const description2 =
        client.connectionType === 'ethernet'
          ? `Eth | ${client.parentId}`
          : `${client.connectionBand || client.connectionInterface || 'N/A'} | ${client.parentId}`;

      return (
        <div
          className='p-xs border-border-flat flex flex-row items-start gap-3 border-b last:border-0'
          role='button'
          tabIndex={0}
          onClick={() => onClickToNavigate(client.parentId, { section: CPESections.CLIENTS })}>
          <div className='w-d10 h-d10 border-gradient-border bg-surface-action flex items-center justify-center rounded-sm border'>
            <img
              // className='mix-blend-hard-light dark:mix-blend-screen'
              src={getDeviceImage(client.deviceType)}
              alt='icon'
            />
          </div>
          <div className='min-w-0 flex-1'>
            <div className='mb-2xs'>
              <AxonTooltipWrapper
                label={
                  <p className='text-content-primary text-md w-full min-w-0 truncate font-medium leading-[120%]'>
                    {client.deviceName}
                  </p>
                }
                content={<p>{client.deviceName}</p>}
              />
            </div>
            <div className='flex flex-col gap-[2px]'>
              <ItemDetail iconName={client.status} description={description1} />
              <ItemDetail iconName={client.connectionType} description={description2} />
            </div>
          </div>
        </div>
      );
    }
    case Slice.SERVICES: {
      const service = data as IService;
      const {
        speedStatus,
        latencyStatus,
        trafficStatus,
        latency,
        uploadSpeed,
        downloadSpeed,
        jitter,
        downloadTraffic,
        uploadTraffic,
      } = service;
      const downloadSpeedDescription = downloadSpeed !== null ? `${downloadSpeed} Mbps` : 'N/A';
      const uploadSpeedDescription = uploadSpeed !== null ? `${uploadSpeed} Mbps` : 'N/A';
      const showSpeed = downloadSpeed !== null && uploadSpeed !== null;
      const speedDescription = `${showSpeed ? `${downloadSpeedDescription} / ${uploadSpeedDescription}` : 'N/A'} ${t('customer:healthCheck.wheel.services.speedDnUp')}`;
      const latencyDescription = (latency !== null ? `${latency} ms` : 'N/A') + ` ${t('customer:healthCheck.latency')}`;
      const jitterDescription = (jitter !== null ? `${jitter} ms` : 'N/A') + ` ${t('customer:healthCheck.jitter')}`;
      const downloadTrafficDescription = downloadTraffic !== null ? `${downloadTraffic} GB` : 'N/A';
      const uploadTrafficDescription = uploadTraffic !== null ? `${uploadTraffic} GB` : 'N/A';
      const showTraffic = downloadTraffic !== null && uploadTraffic !== null;
      const trafficDescription = `${showTraffic ? `${downloadTrafficDescription} / ${uploadTrafficDescription}` : 'N/A'} ${t('customer:healthCheck.wheel.services.trafficDnUp')}`;

      return (
        <div className='p-xs border-border-flat flex flex-row items-start gap-3 border-b last:border-0'>
          <div className='w-d10 h-d10 border-gradient-border bg-surface-action flex items-center justify-center rounded-sm border'>
            <img src={getDeviceImage(service.logo)} />
          </div>
          <div className='min-w-0 flex-1'>
            <div className='mb-2xs'>
              <AxonTooltipWrapper
                label={
                  <p className='text-content-tertiary font-suisses font-book w-full min-w-0 truncate text-xs leading-[120%]'>
                    {service.name}
                  </p>
                }
                content={<p>{service.name}</p>}
              />
            </div>
            <div className='flex flex-col'>
              <div className='mb-xs'>
                <ItemDetail variant='bold' iconName={service.status} description={getStatusText(service.status)} />
              </div>
              <div className='flex flex-col gap-1'>
                <ItemDetail iconName={speedStatus || 'unknown'} description={speedDescription} />
                <ItemDetail iconName={latencyStatus || 'unknown'} description={latencyDescription} />
                <ItemDetail iconName={'unknown'} description={jitterDescription} />
                <ItemDetail iconName={trafficStatus || 'unknown'} description={trafficDescription} />
                {/* <ItemDetail iconName={cpe.cpeStatus} description={firmwareDescripption} />
            <ItemDetail iconName={cpe.cpeStatus} description={lastRebootDescription} />
            <ItemDetail iconName={cpe.cpeStatus} description={rebootCountDescription} />
            <ItemDetail iconName={cpe.cpeStatus} description={cpuStatusDescription} />
            <ItemDetail iconName={cpe.cpeStatus} description={freeMemoryStatusDescription} /> */}
              </div>
            </div>
          </div>
        </div>
      );
    }

    case Slice.CPE: {
      const cpe = data as ICpe;
      const installedRelativeTime = getRelativeTimeFromNow(Number(cpe.installedTime));
      const installedDescription = installedRelativeTime
        ? `${t('customer:healthCheck.wheel.cpe.installed')} ${installedRelativeTime}`
        : `N/A ${t('customer:healthCheck.wheel.cpe.installed')}`;
      const firmwareDescripption = cpe.cpeFirmwareVersion
        ? `${t('customer:healthCheck.wheel.cpe.firmware')} ${cpe.cpeFirmwareVersion}`
        : `N/A ${t('customer:healthCheck.wheel.cpe.firmware')}`;
      const lastRebootRelativeTime = getRelativeTimeFromNow(Number(cpe.lastRebootTime));
      const lastRebootDescription = lastRebootRelativeTime
        ? `${t('customer:healthCheck.wheel.cpe.lastReboot')} ${lastRebootRelativeTime}`
        : `N/A ${t('customer:healthCheck.wheel.cpe.lastReboot')}`;
      const rebootCountDescription =
        cpe.powerCycle !== null
          ? `${cpe.powerCycle} ${t('customer:healthCheck.wheel.cpe.rebootCountDescription')}`
          : '';
      const cpuStatusDescription = `${getStatusText(cpe.cpuStatus)} ${t('customer:healthCheck.wheel.cpe.cpuStatusDescription')}`;
      const freeMemoryStatusDescription = `${getStatusText(cpe.freeMemoryStatus)} ${t('customer:healthCheck.wheel.cpe.freeMemoryStatus')}`;
      return (
        <div className='p-xs border-border-flat flex flex-row items-start gap-3 border-b last:border-0'>
          <div className='w-d10 h-d10 border-gradient-border bg-surface-action flex items-center justify-center rounded-sm border'>
            <img src={getDeviceImage(cpe.cpeType)} />
          </div>
          <div className='min-w-0 flex-1'>
            <div role='button' tabIndex={0} className='flex flex-col' onClick={() => onClickToNavigate(cpe.cpeId)}>
              <div className='mb-2xs'>
                <AxonTooltipWrapper
                  label={
                    <p className='text-content-tertiary font-suisses font-book w-full min-w-0 truncate text-xs leading-[120%]'>
                      {cpe.cpeType} / {cpe.cpeId}
                    </p>
                  }
                  content={
                    <p>
                      {cpe.cpeType} / {cpe.cpeId}
                    </p>
                  }
                />
              </div>
            </div>
            <div className='flex flex-col'>
              <div className='mb-xs'>
                <ItemDetail variant='bold' iconName={cpe.cpeStatus} description={getStatusText(cpe.cpeStatus)} />
              </div>
              <div className='flex flex-col gap-1'>
                <ItemDetail description={installedDescription} />
                <ItemDetail iconName={cpe.firmwareStatus} description={firmwareDescripption} />
                <ItemDetail iconName={'stable'} description={lastRebootDescription} />
                <ItemDetail iconName={'stable'} description={rebootCountDescription} />
                <ItemDetail iconName={cpe.cpuStatus} description={cpuStatusDescription} />
                <ItemDetail iconName={cpe.freeMemoryStatus} description={freeMemoryStatusDescription} />
              </div>
            </div>
          </div>
        </div>
      );
    }

    case Slice.LAN_WLAN: {
      const lanWlan = data as INetwork;
      return (
        <div className='p-xs border-border-flat flex flex-row items-start gap-3 border-b last:border-0'>
          <div className='w-d10 h-d10 border-gradient-border bg-surface-action flex items-center justify-center rounded-sm border'>
            <img src={getDeviceImage(lanWlan.cpeType)} />
          </div>
          <div className='min-w-0 flex-1'>
            <div className='mb-2xs'>
              <AxonTooltipWrapper
                label={
                  <p
                    className='text-content-tertiary font-suisses font-book w-full min-w-0 truncate text-xs leading-[120%]'
                    role='button'
                    tabIndex={0}
                    onClick={() =>
                      onClickToNavigate(lanWlan.cpeId, {
                        section: CPESections.LAN_WLAN,
                      })
                    }>
                    {lanWlan.cpeType} / {lanWlan.cpeId}
                  </p>
                }
                content={
                  <p>
                    {lanWlan.cpeType} / {lanWlan.cpeId}
                  </p>
                }
              />
            </div>
            <div className='flex flex-col'>
              <div className='flex flex-col gap-2'>
                {lanWlan.connections.map((connection) => {
                  const { name, connectionType, band, duplexMode, isActive, status, networkType } = connection;
                  const isEthernet = connectionType === 'ethernet';
                  const description1 = isEthernet
                    ? `${name}` +
                      (duplexMode ? ` / (${duplexMode} ${t('customer:healthCheck.wheel.lanWlan.duplex')})` : '')
                    : `${band || 'N/A'} ${networkType === 'Mesh' ? 'Mesh' : ''} (${name})`;

                  const description2 = isEthernet
                    ? isActive
                      ? t('customer:healthCheck.wheel.lanWlan.inUse')
                      : t('customer:healthCheck.wheel.lanWlan.notInUse')
                    : `${getStatusText(status)}`;

                  return (
                    <div
                      className='flex flex-col gap-1'
                      role='button'
                      tabIndex={0}
                      key={name}
                      onClick={() =>
                        onClickToNavigate(lanWlan.cpeId, {
                          section: CPESections.LAN_WLAN,
                          widget: isEthernet ? CPEWidgets.LAN_PORTS : CPEWidgets.WIFI_BANDS,
                        })
                      }>
                      <ItemDetail iconName={connectionType} description={description1} />
                      <ItemDetail iconName={status || 'unknown'} description={description2} variant='bold' />
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      );
    }
    default:
      return null;
  }
}

function Footer(props: { type: Slice; overview: IProps['overview']; footerInfo?: IFooterInfo }) {
  const { t } = useTranslation();
  const { type, footerInfo } = props;

  switch (type) {
    case Slice.CLIENTS: {
      return (
        <footer className='bg-surface-popover border-border-flat p-md sticky inset-x-0 bottom-0 flex flex-col gap-[2px] border-t'>
          <div className='gap-xs text-content-tertiary font-suisse font-book mb-1 flex flex-wrap items-center text-xs leading-[120%]'>
            <div className='flex items-center gap-1'>
              <BoldLinkRoundAngle className='text-content-primary size-4' />
              <span>
                {t('customer:healthCheck.wheel.footer.eth')}: {footerInfo?.ethernetConnection}
              </span>
              <AxonSeparator orientation='vertical' className='h-[10px]' />
              <span>
                {t('customer:healthCheck.wheel.footer.wifi')}: {footerInfo?.wifiConnection}
              </span>
              {Number(footerInfo?.unknownConnection) > 0 && (
                <>
                  <AxonSeparator orientation='vertical' className='h-[10px]' />
                  <span>
                    {t('customer:healthCheck.wheel.footer.unknown')}: {footerInfo?.unknownConnection}
                  </span>
                </>
              )}
            </div>
          </div>
          <div className='gap-xs text-content-tertiary font-suisse font-book mb-1 flex items-center text-xs leading-[120%]'>
            <BoldTranslation className='text-content-primary size-4' />
            <div className='gap-xs text-content-tertiary font-suisse font-book flex items-center text-xs leading-[120%]'>
              <div className='gap-xs flex items-center'>
                <Circle className='fill-content-meta-green text-gradient-border size-2' />
                <span>{footerInfo?.stable ?? 'N/A'}</span>
              </div>
              <AxonSeparator orientation='vertical' className='h-[10px]' />
              <div className='gap-xs flex items-center'>
                <Triangle className='fill-content-meta-orange text-gradient-border size-2' />
                <span>{footerInfo?.unstable ?? 'N/A'}</span>
              </div>
              <AxonSeparator orientation='vertical' className='h-[10px]' />
              <div className='gap-xs flex items-center'>
                <CloseIcon className='text-content-meta-red size-2' />
                <span>{footerInfo?.veryUnstable ?? 'N/A'}</span>
              </div>
              <AxonSeparator orientation='vertical' className='h-[10px]' />
              <div className='gap-xs flex items-center'>
                <Signifier className='text-content-disabled size-2' />
                <span>{footerInfo?.disconnected ?? 'N/A'}</span>
              </div>
            </div>
          </div>
        </footer>
      );
    }

    case Slice.LAN_WLAN: {
      // if (!overview?.descriptions) {
      //   return null;
      // }
      // return (
      //   <footer className='bg-surface-popover border-border-flat p-md sticky inset-x-0 bottom-0 flex flex-col gap-[2px] border-t'>
      //     {overview.descriptions?.map((description, idx) => (
      //       <p className='text-content-tertiary font-suisse font-book text-xs leading-[120%]' key={idx}>
      //         {description}
      //       </p>
      //     ))}
      //   </footer>
      // );
    }
  }
  return null;
}

export default Detail;
