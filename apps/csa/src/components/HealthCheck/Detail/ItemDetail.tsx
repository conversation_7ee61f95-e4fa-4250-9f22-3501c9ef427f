import { useMemo } from 'react';
import { GitMerge, Polygon2, Signifier, WiFi, X } from 'ui/UIAssets';

export default function ItemDetail(props: {
  iconName?: string | null;
  description: string;
  variant?: 'normal' | 'bold';
  color?: string;
}) {
  const { iconName, description, variant = 'normal', color } = props;

  let textColor = color || 'rgb(var(--content-tertiary))';

  if (iconName === 'unstable') {
    textColor = 'rgb(var(--content-meta-orange))';
  } else if (iconName === 'veryUnstable') {
    textColor = 'rgb(var(--content-meta-red))';
  }

  const styleDescription = {
    text: {
      fontSize: variant === 'bold' ? 'var(--font-size-md)' : 'var(--font-size-xs)',
      color: textColor,
    },
  };

  const icon = useMemo(() => {
    const style = {
      width: variant === 'bold' ? '12px' : '8px',
      height: variant === 'bold' ? '12px' : '8px',
      fontSize: variant === 'bold' ? 'var(--font-size-md)' : 'var(--font-size-xs)',
    };

    if (iconName === 'stable') {
      return <Signifier style={style} className='fill-content-meta-green text-content-meta-green' />;
    }
    if (iconName === 'unstable') {
      return <Polygon2 style={style} className='fill-content-meta-orange text-content-meta-orange' />;
    }
    if (iconName === 'veryUnstable') {
      return <X style={style} className='fill-content-meta-red text-content-meta-red' />;
    }
    if (iconName === 'disconnected' || iconName === 'unknown') {
      return <Signifier style={style} className='fill-content-tertiary text-content-tertiary' />;
    }
    if (iconName === 'wifi') {
      return <WiFi style={style} className='text-content-tertiary' />;
    }
    if (iconName === 'ethernet') {
      return <GitMerge style={style} className='text-content-tertiary' />;
    }
    return null;
  }, [iconName, variant]);

  return (
    <div className='flex flex-row items-center gap-1'>
      {icon}
      <span
        style={styleDescription.text}
        className='text-content-tertiary font-suisse font-book text-xs leading-[120%]'>
        {description}
      </span>
    </div>
  );
}
