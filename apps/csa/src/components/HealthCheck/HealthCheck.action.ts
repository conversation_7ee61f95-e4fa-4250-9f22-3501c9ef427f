import { useGetHealthCheck } from 'services/HealthCheck';
import { useGetRealtimeHealthcheck } from 'services/Realtime';
import { useMemo } from 'react';
import { Slice } from './types';
import useTabStore from '@/stores/tab.store';
import get from 'lodash/get';
import groupBy from 'lodash/groupBy';
import { getSliceText } from './utils/wheel.util';
import { useShallow } from 'zustand/react/shallow';

export const useHealthCheckAction = () => {
  const { activeTab } = useTabStore(
    useShallow((state) => ({
      activeTab: state.activeTab,
    })),
  );

  const customerId = get(activeTab, 'customerId') || '';

  const {
    data: healthCheckHistoricalData,
    isLoading: isLoadingHealthCheckHistoricalData,
    ...rest
  } = useGetHealthCheck(
    {
      customerId,
    },
    { enabled: !!customerId },
  );

  const realtimeRequestId = get(activeTab, 'config.realtime.realtimeRequestId', '');
  const dataIsReady = get(activeTab, 'config.realtime.dataIsReady', false);
  const enableRealtimeRequesting = get(activeTab, 'config.realtime.enabled', false);

  const { data: realtimeOverviewData, isFetching: isFetchingHealthcheckRealtime } = useGetRealtimeHealthcheck(
    {
      customerId,
      realtimeRequestId,
    },
    {
      enabled: dataIsReady,
    },
  );

  const isRequestingRealtime = isFetchingHealthcheckRealtime || enableRealtimeRequesting;
  const healthCheckData = isRequestingRealtime ? undefined : realtimeOverviewData || healthCheckHistoricalData;
  const overview = get(healthCheckData, 'data.overview');

  const lastUpdatedTimestamp = get(healthCheckData, 'data.lastUpdatedTimestamp');

  const data = useMemo(() => {
    // client data
    const client = get(healthCheckData, 'data.details.clients') ?? [];
    const connectionTypeGroup = groupBy(client, 'connectionType');
    const ethernetConnection = connectionTypeGroup.ethernet?.length || 0;
    const wifiConnection = connectionTypeGroup.wifi?.length || 0;
    const numberUnknownConnectionType = client.length - ethernetConnection - wifiConnection;

    // cpe data
    const cpe = get(healthCheckData, 'data.details.cpes') ?? [];
    // lan/wlan data
    const lanWlan = get(healthCheckData, 'data.details.networks') ?? [];
    // wan data
    const wan = get(healthCheckData, 'data.details.wans');
    // service data
    const service = get(healthCheckData, 'data.details.services') ?? [];

    const result = {
      clients: {
        title: getSliceText(Slice.CLIENTS),
        data: client,
        description: client.length,
        footerInfo: {
          stable: get(overview, 'clients.stable') ?? 0,
          unstable: get(overview, 'clients.unstable') ?? 0,
          veryUnstable: get(overview, 'clients.veryUnstable') ?? 0,
          disconnected: get(overview, 'clients.disconnected') ?? 0,
          ethernetConnection,
          wifiConnection,
          unknownConnection: numberUnknownConnectionType,
        },
      },
      services: {
        title: getSliceText(Slice.SERVICES),
        data: service || [],
      },
      cpes: {
        title: getSliceText(Slice.CPE),
        data: cpe || [],
      },
      networks: {
        title: getSliceText(Slice.LAN_WLAN),
        data: lanWlan || [],
        footerInfo: {},
      },
      wans: {
        title: getSliceText(Slice.WAN),
        data: wan || [],
      },
    };

    return result;
  }, [overview, healthCheckData]);

  return {
    ...rest,
    data,
    overview,
    isLoading: isRequestingRealtime || isLoadingHealthCheckHistoricalData,
    lastUpdatedTimestamp,
  };
};
