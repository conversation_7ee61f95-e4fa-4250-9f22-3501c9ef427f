import { convertQOEStatus, QOEStatusEnum } from '@/utils/QOE.util';
import { CpeStatusIcon } from '@/pages/Device/CpeStatistics/icons/CpeStatusIcon';
import { cn } from '@/utils';
import { getRelativeTimeFromNow } from 'services/Utils';

interface StatisticWidgetStatusProps {
  qoeValue?: number;
  lastUpdated?: number;
  lastBootupTime?: number;
}

export const StatisticWidgetStatus = ({ qoeValue, lastBootupTime, lastUpdated }: StatisticWidgetStatusProps) => {
  if ((typeof qoeValue === 'number' && typeof lastUpdated === 'number') || typeof lastBootupTime === 'number') {
    const qoeStatus = convertQOEStatus(qoeValue);

    return (
      <div className='flex w-full items-center justify-start gap-1'>
        <CpeStatusIcon
          className={cn(`text-content-disabled`, {
            'text-content-meta-green': qoeStatus === QOEStatusEnum.STABLE,
            'text-content-meta-yellow': qoeStatus === QOEStatusEnum.UNSTABLE,
            'text-content-meta-red': qoeStatus === QOEStatusEnum.VERY_UNSTABLE,
          })}
        />
        <span className='flex items-center gap-1 text-xs'>
          <span
            className={cn(`text-content-disabled font-medium`, {
              'text-content-meta-green': qoeStatus === QOEStatusEnum.STABLE,
              'text-content-meta-yellow': qoeStatus === QOEStatusEnum.UNSTABLE,
              'text-content-meta-red': qoeStatus === QOEStatusEnum.VERY_UNSTABLE,
            })}>
            {qoeStatus}
          </span>
          <span className='font-book text-content-primary opacity-60'>
            since {lastUpdated ? getRelativeTimeFromNow(lastUpdated) : 'Unknown'}
          </span>
          {lastBootupTime && (
            <span className='font-book text-content-primary opacity-60'>
              (Active since {getRelativeTimeFromNow(lastBootupTime)})
            </span>
          )}
        </span>
      </div>
    );
  }

  return (
    <div className='flex w-full items-center justify-start gap-1'>
      <CpeStatusIcon className='text-content-disabled' />
      <p className='font-book text-content-tertiary'>No Status Data Available</p>
    </div>
  );
};
