import { useStartRealtimeProcess } from 'services/Realtime';
import get from 'lodash/get';
import useTabStore from '@/stores/tab.store';
import { useShallow } from 'zustand/react/shallow';

export const useCustomerAction = () => {
  const { addConfig, activeTab } = useTabStore(
    useShallow((state) => ({
      addConfig: state.addConfig,
      activeTab: state.activeTab,
    })),
  );

  const activeCustomerId = get(activeTab, 'customerId') || '';
  const { startRealtimeProcess } = useStartRealtimeProcess();
  const enableRealtimeRequesting = get(activeTab, 'config.realtime.enableRealtimeRequesting', false);

  const handleGetRealtime = () => {
    addConfig({
      realtime: {
        enableRealtimeRequesting: true,
      },
    });
    startRealtimeProcess(
      {
        customerId: activeCustomerId,
      },
      addConfig,
    );
  };
  return { handleGetRealtime, isLoading: enableRealtimeRequesting };
};
