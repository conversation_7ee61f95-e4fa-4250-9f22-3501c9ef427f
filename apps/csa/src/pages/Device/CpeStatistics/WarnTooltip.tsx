import { CircleAlert } from 'lucide-react';

import { cn } from '@/utils';
import { AxonTooltip, AxonTooltipContent, AxonTooltipProvider, AxonTooltipTrigger } from 'ui/UIComponents';
import { DetectionStatusEnum } from '@/utils/detection.util';

type WarnTooltipProps = {
  detectionStatus: DetectionStatusEnum;
};

export function CpeStatWarnTooltip({ detectionStatus }: WarnTooltipProps) {
  return (
    <AxonTooltipProvider>
      <AxonTooltip>
        <AxonTooltipTrigger asChild>
          <CircleAlert
            className={cn('rounded-full border-none', {
              'fill-content-meta-yellow/30 text-content-meta-yellow': DetectionStatusEnum.UNSTABLE === detectionStatus,
              'fill-content-meta-red/30 text-content-meta-red': DetectionStatusEnum.VERY_UNSTABLE === detectionStatus,
            })}
            size={16}
          />
        </AxonTooltipTrigger>
        <AxonTooltipContent side='bottom' className={cn('w-auto p-0')} align='start'>
          <div
            className={cn('flex items-center gap-x-2 px-2 py-1', {
              'text-content-meta-yellow': DetectionStatusEnum.UNSTABLE === detectionStatus,
              'text-content-meta-red': DetectionStatusEnum.VERY_UNSTABLE === detectionStatus,
            })}>
            <div
              className={cn('rounded-full p-0.5', {
                'bg-content-meta-yellow': DetectionStatusEnum.UNSTABLE === detectionStatus,
                'bg-content-meta-red': DetectionStatusEnum.VERY_UNSTABLE === detectionStatus,
              })}
            />
            <span className='font-semibold'>{detectionStatus}</span>
          </div>
        </AxonTooltipContent>
      </AxonTooltip>
    </AxonTooltipProvider>
  );
}
