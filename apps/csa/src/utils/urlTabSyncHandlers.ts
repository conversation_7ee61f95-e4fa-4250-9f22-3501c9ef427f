import { Tab, TabStore, getDefaultTimeRange } from '@/stores/tab.store';

type BaseUrlParams = {
  allTabs: Tab[];
  addTab: TabStore['addTab'];
  setActiveTab: TabStore['setActiveTab'];
};

type HandleCustomerUrlParams = BaseUrlParams & {
  customerId: string;
};

export const handleCustomerUrl = ({ customerId, allTabs, addTab, setActiveTab }: HandleCustomerUrlParams) => {
  // Find existing customer tab
  let tab = allTabs.find((t) => t.id === customerId && t.type === 'customer');

  // If not found, create a new one
  if (!tab) {
    tab = {
      id: customerId,
      name: customerId,
      type: 'customer',
      deviceType: null,
      deviceId: null,
      customerId,
      timeRangeSelected: getDefaultTimeRange(),
    };
    addTab(tab);
  }

  // Set the tab as active
  setActiveTab({ tabId: tab.id, tabType: tab.type });
};

type HandleDeviceUrlParams = HandleCustomerUrlParams & {
  deviceId: string;
};

export const handleDeviceUrl = ({ customerId, deviceId, allTabs, addTab, setActiveTab }: HandleDeviceUrlParams) => {
  // Find existing device tab
  let tab = allTabs.find((t) => t.id === deviceId && t.type === 'device');

  // If not found, create a new one
  if (!tab) {
    tab = {
      id: deviceId,
      name: deviceId,
      type: 'device',
      deviceType: null,
      deviceId,
      customerId,
      timeRangeSelected: getDefaultTimeRange(),
    };
    addTab(tab);
  }

  // Set the tab as active
  setActiveTab({ tabId: tab.id, tabType: tab.type });
};

type SyncTabsFromUrlParams = BaseUrlParams & {
  searchParams: URLSearchParams;
};

export const syncTabsFromUrl = ({ searchParams, allTabs, addTab, setActiveTab }: SyncTabsFromUrlParams) => {
  const customerId = searchParams.get('customerId');
  const deviceId = searchParams.get('deviceId');

  if (!customerId) return;

  if (!deviceId) {
    // Handle customer URL: ?customerId={customerId}
    handleCustomerUrl({
      customerId,
      allTabs,
      addTab,
      setActiveTab,
    });
  } else {
    // Handle device URL: ?customerId={customerId}&deviceId={deviceId}
    handleDeviceUrl({
      customerId,
      deviceId,
      allTabs,
      addTab,
      setActiveTab,
    });
  }
};

export const getTabUrlSearchParams = ({
  id,
  type,
  customerId,
  anchor,
}: {
  id: Tab['id'];
  type: Tab['type'];
  customerId?: Tab['customerId'];
  anchor?: string;
}) => {
  const params = new URLSearchParams();
  if (type === 'customer') {
    params.set('customerId', id);
  }

  if (type === 'device') {
    params.set('deviceId', id);

    if (customerId) {
      params.set('customerId', customerId);
    }

    if (anchor) {
      params.set('anchor', anchor);
    }
  }

  return params.toString();
};
