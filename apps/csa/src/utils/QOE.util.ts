export enum QOEStatusEnum {
  STABLE = 'Stable',
  UNSTABLE = 'Unstable',
  VERY_UNSTABLE = 'Very Unstable',
  UNKNOWN = 'Unknown',
}

export const convertQOEStatus = (status: number | null | undefined): QOEStatusEnum => {
  if (typeof status === 'number') {
    if (status >= 4) {
      return QOEStatusEnum.STABLE;
    }

    if (status >= 2) {
      return QOEStatusEnum.UNSTABLE;
    }

    if (status >= 0) {
      return QOEStatusEnum.VERY_UNSTABLE;
    }
  }

  return QOEStatusEnum.UNKNOWN;
};
