import useTabStore from '@/stores/tab.store';
import { syncTabsFromUrl } from '@/utils/urlTabSyncHandlers';
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useShallow } from 'zustand/react/shallow';

const useUrlTabSync = () => {
  const location = useLocation();
  const { addTab, allTabs, setActiveTab } = useTabStore(
    useShallow((state) => ({
      addTab: state.addTab,
      allTabs: state.allTabs,
      setActiveTab: state.setActiveTab,
    })),
  );

  useEffect(() => {
    // Only apply on csa page
    if (location.pathname !== '/csa') return;

    const searchParams = new URLSearchParams(location.search);

    syncTabsFromUrl({
      searchParams,
      allTabs,
      addTab,
      setActiveTab,
    });
  }, [location.pathname, location.search, allTabs, addTab, setActiveTab]);

  return null;
};

export default useUrlTabSync;
