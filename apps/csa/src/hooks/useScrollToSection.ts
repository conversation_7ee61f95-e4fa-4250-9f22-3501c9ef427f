import { CPESections, CPEWidgets, WIDGET_TO_SECTION } from '@/constants/cpeSections';
import { useEffect } from 'react';
import { flushSync } from 'react-dom';
import { useLocation } from 'react-router-dom';

export type SectionState = {
  showCpe: boolean;
  showLanWLan: boolean;
  showClientWidgets: boolean;
  showWanWidgets: boolean;
  showServicesWidget: boolean;
};

/**
 * Handling scroll-to-section functionality on device page
 * @param setState - State setter for section visibility
 * @param status     Device status
 */
export const useScrollToSection = (
  setState: React.Dispatch<React.SetStateAction<SectionState>>,
  status: string | undefined,
) => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const anchor = searchParams.get('anchor');

  // Wait for checking device status in Device component
  // Also, only scroll if device is Online
  useEffect(() => {
    if (!anchor || status !== 'Online') return;

    let section = '';
    let widget = '';

    if (Object.values(CPESections).includes(anchor)) {
      section = anchor;
    } else if (Object.values(CPEWidgets).includes(anchor)) {
      section = WIDGET_TO_SECTION[anchor] || '';
      widget = anchor;
    }
    // Update state synchronously to ensure DOM is ready for scrolling
    if (section) {
      flushSync(() => {
        setState({
          showCpe: section === CPESections.CPE,
          showLanWLan: section === CPESections.LAN_WLAN,
          showClientWidgets: section === CPESections.CLIENTS,
          showWanWidgets: section === CPESections.WAN,
          showServicesWidget: section === CPESections.SERVICES,
        });
      });
    }

    // Determine scroll target
    const scrollToId = widget || section;
    if (!scrollToId) return;

    // Since we are using state change, then we use 2 requestAnimationFrame to ensure the DOM is updated before scrolling
    const frameId = requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        const nodeToScrollTo = document.getElementById(scrollToId);
        if (!nodeToScrollTo) return;

        const mainContent = document.getElementById('main-content');

        if (mainContent) {
          // Calculate scroll position with header offset 116px
          const top = nodeToScrollTo.getBoundingClientRect().top + mainContent.scrollTop - 72; // 56 is the header height + 16px offset
          mainContent.scrollTo({ top, behavior: 'smooth' });
        } else {
          // Fallback to standard scrollIntoView
          nodeToScrollTo.scrollIntoView({ behavior: 'smooth' });
        }
      });
    });

    // Cleanup animation frame on unmount
    return () => cancelAnimationFrame(frameId);
  }, [anchor, setState, status]);
};
