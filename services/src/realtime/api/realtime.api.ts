import api from '@/api/apiService';
import { IHealthCheck } from '@/healthCheck/types/healthCheck.types';
import { BFFResponseDTO } from '@/types';
import { ENDPOINTS } from '../constants/realtime.endpoint';
import { IRealtimeRequestIdRes, RealtimeParams, IRealtimeStatus } from '../types/realtime.type';
import pollingRequest from '@/utils/polling.util';
import get from 'lodash/get';
import { InsightReponse } from '@/insight/types/insightTypes';

export const getRealtimeDataRequestId = async (
  params: RealtimeParams,
): Promise<BFFResponseDTO<IRealtimeRequestIdRes>> => {
  const response = await api.get(ENDPOINTS.REALTIME_REQUEST_ID, { params });
  return response.data;
};

export const getRealtimeDataStatus = async (
  params: Required<RealtimeParams>,
): Promise<BFFResponseDTO<IRealtimeStatus>> => {
  const response = await pollingRequest<IRealtimeStatus>(
    {
      url: ENDPOINTS.REALTIME_STATUS,
      params,
    },
    {
      endCondition: (data) => {
        return get(data, 'realtimeRequestFinished');
      },
    },
  );
  if (!response.data) {
    throw new Error('Healthcheck request failed');
  }
  return response.data;
};

export const getHealthcheckRealtime = async (
  params: Required<RealtimeParams>,
): Promise<BFFResponseDTO<IHealthCheck>> => {
  const response = await api.get(ENDPOINTS.REALTIME_HEALTH_CHECK, { params });

  return response.data;
};

export const getInsightRealtime = async (params: Required<RealtimeParams>): Promise<BFFResponseDTO<InsightReponse>> => {
  const response = await api.get(ENDPOINTS.REALTIME_INSIGHT, { params });

  return response.data;
};
