import { ENDPOINTS } from './realtime.endpoint';
import { RealtimeParams } from '../types/realtime.type';

export const QUERY_KEYS = {
  requestRealtimeId: (params: RealtimeParams) => [ENDPOINTS.REALTIME_REQUEST_ID, params],
  realtimeHealthcheck: (params: RealtimeParams) => [ENDPOINTS.REALTIME_HEALTH_CHECK, params],
  realtimeInsight: (params: RealtimeParams) => [ENDPOINTS.REALTIME_INSIGHT, params],
  realtimeStatus: (params: RealtimeParams) => [ENDPOINTS.REALTIME_STATUS, params],
};
