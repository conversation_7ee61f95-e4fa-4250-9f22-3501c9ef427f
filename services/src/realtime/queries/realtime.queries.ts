import { AxonQueryOptions, BFFResponseDTO } from '../../types';
import { useQuery, useIsFetching, Query } from '@tanstack/react-query';
import { RealtimeParams, RealtimeMetric } from '../types/realtime.type';
import { QUERY_KEYS } from '../constants/realtime.queryKeys';
import { ENDPOINTS, REALTIME_ENDPOINT } from '../constants/realtime.endpoint';
import {
  getRealtimeDataRequestId,
  getRealtimeDataStatus,
  getHealthcheckRealtime,
  getInsightRealtime,
} from '../api/realtime.api';
import { IHealthCheck } from '@/healthCheck/types/healthCheck.types';
import get from 'lodash/get';
import { InsightReponse } from '@/insight/types/insightTypes';
import { useCallback } from 'react';

export const useStartRealtimeProcess = () => {
  const startRealtimeProcess = useCallback(
    async (params: RealtimeParams, onFinish: (config: any, customerId: string) => void) => {
      try {
        const requestIdRes = await getRealtimeDataRequestId(params);
        const requestId = get(requestIdRes, 'data.requestId') ?? '';
        const statusRes = await getRealtimeDataStatus({
          ...params,
          realtimeRequestId: requestId,
        });
        const status = get(statusRes, 'data.status') ?? '';
        const dataIsReady = status === 'SUCCESS';
        onFinish(
          {
            realtime: {
              enableRealtimeRequesting: false,
              realtimeRequestId: requestId,
              dataIsReady,
            },
          },
          params.customerId,
        );
      } catch (error) {
        onFinish(
          {
            realtime: {
              enableRealtimeRequesting: false,
              realtimeRequestId: '',
              dataIsReady: false,
            },
          },
          params.customerId,
        );
      }
    },
    [],
  );
  return {
    startRealtimeProcess,
  };
};

/**
 * @param customerId - customerId, used to distinguish between different customers queries (base on tabs)
 * @param fnToCheck - function to check if the query is realtime.
 * @returns - number of realtime queries is requesting
 */
export const useIsRealtimeRequesting = (
  customerId: string,
  metric: RealtimeMetric,
  fnToCheck?: (queryKey: Query) => boolean,
) => {
  function getConditionForMetric(query: Query) {
    const [queryKey, queryParams] = query.queryKey as [string, any];
    switch (metric) {
      case 'healthCheck':
        return (
          queryParams?.customerId === customerId &&
          (queryKey === ENDPOINTS.REALTIME_STATUS || queryKey === ENDPOINTS.REALTIME_REQUEST_ID)
        );
      case 'insight':
        return (
          queryParams?.customerId === customerId &&
          (queryKey === ENDPOINTS.REALTIME_STATUS || queryKey === ENDPOINTS.REALTIME_REQUEST_ID)
        );
      default: {
        return queryKey.includes(REALTIME_ENDPOINT) && queryParams?.customerId === customerId;
      }
    }
  }
  const numberOfRealtimeIsRequestingForHealthCheck = useIsFetching({
    predicate: (query) => {
      if (fnToCheck) {
        return fnToCheck(query);
      } else {
        return getConditionForMetric(query);
      }
    },
  });

  return numberOfRealtimeIsRequestingForHealthCheck > 0;
};

export const useGetRealtimeHealthcheck = (
  params: Required<RealtimeParams>,
  options?: AxonQueryOptions<BFFResponseDTO<IHealthCheck>>,
) => {
  const isRealtimeRequesting = useIsRealtimeRequesting(params.customerId, 'healthCheck');

  const query = useQuery({
    queryKey: QUERY_KEYS.realtimeHealthcheck(params),
    queryFn: () => getHealthcheckRealtime(params),
    enabled: Boolean(params.realtimeRequestId),
    retry: false,
    staleTime: Infinity,
    gcTime: Infinity,
    ...options,
  });

  return {
    ...query,
    isRequesting: isRealtimeRequesting,
  };
};

export const useGetRealtimeInsight = (
  params: Required<RealtimeParams>,
  options?: AxonQueryOptions<BFFResponseDTO<InsightReponse>>,
) => {
  const isRealtimeRequesting = useIsRealtimeRequesting(params.customerId, 'insight');

  const query = useQuery({
    queryKey: QUERY_KEYS.realtimeInsight(params),
    queryFn: () => getInsightRealtime(params),
    enabled: Boolean(params.realtimeRequestId),
    retry: false,
    ...options,
  });

  return {
    ...query,
    isRequesting: isRealtimeRequesting,
  };
};
